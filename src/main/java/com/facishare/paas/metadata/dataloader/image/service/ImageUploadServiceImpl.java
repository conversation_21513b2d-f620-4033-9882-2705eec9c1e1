package com.facishare.paas.metadata.dataloader.image.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.paas.metadata.dataloader.image.model.ImageUploadResult;
import com.facishare.paas.metadata.dataloader.model.User;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 图片上传服务
 * 负责将图片数据上传到文件服务器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class ImageUploadServiceImpl implements ImageUploadService {

    @Autowired
    private StoneProxyApi stoneProxyApi;

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 支持的图片格式
     */
    private static final Set<String> SUPPORTED_FORMATS = new HashSet<>(Arrays.asList(
            "png", "jpg", "jpeg", "gif", "bmp", "webp"
    ));

    /**
     * 最大文件大小（10MB）
     */
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;


    /**
     * 上传单个图片
     *
     * @param imageData 图片数据
     * @param fileName 文件名
     * @param format 图片格式
     * @param user 用户信息
     * @return 上传结果
     */
    @Override
    public ImageUploadResult uploadImage(byte[] imageData, String fileName, String format, User user) {
        long startTime = System.currentTimeMillis();

        try {
            // 验证输入参数
            ImageUploadResult validationResult = validateUploadParameters(imageData, fileName, format);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }
            // 执行上传 - 按照Stone API的实际调用方式
            try (InputStream inputStream = new ByteArrayInputStream(imageData)) {
                // 构建Stone上传请求
                String uploadedPath = uploadToStoneService(inputStream, fileName, format, imageData.length, user);
                long duration = System.currentTimeMillis() - startTime;
                log.debug("Image uploaded successfully: {} -> {}, size: {} bytes, duration: {}ms",
                        fileName, uploadedPath, imageData.length, duration);
                return ImageUploadResult.builder()
                        .success(true)
                        .uploadedPath(uploadedPath)
                        .originalFileName(fileName)
                        .fileSize(imageData.length)
                        .format(format)
                        .uploadDuration(duration)
                        .build();
            }
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Error uploading image: {}, size: {} bytes, duration: {}ms",
                    fileName, imageData != null ? imageData.length : 0, duration, e);
            return ImageUploadResult.builder()
                    .success(false)
                    .errorMessage("Upload failed: " + e.getMessage())
                    .originalFileName(fileName)
                    .fileSize(imageData != null ? imageData.length : 0)
                    .format(format)
                    .uploadDuration(duration)
                    .build();
        }
    }

    /**
     * 验证上传参数
     *
     * @param imageData 图片数据
     * @param fileName 文件名
     * @param format 图片格式
     * @return 验证结果
     */
    private ImageUploadResult validateUploadParameters(byte[] imageData, String fileName, String format) {
        // 检查图片数据
        if (imageData == null || imageData.length == 0) {
            return ImageUploadResult.builder()
                    .success(false)
                    .errorMessage("Image data is null or empty")
                    .originalFileName(fileName)
                    .build();
        }

        // 检查文件大小
        if (imageData.length > MAX_FILE_SIZE) {
            return ImageUploadResult.builder()
                    .success(false)
                    .errorMessage("File size exceeds maximum limit: " + formatFileSize(MAX_FILE_SIZE))
                    .originalFileName(fileName)
                    .fileSize(imageData.length)
                    .build();
        }

        // 检查文件格式
        if (format != null && !SUPPORTED_FORMATS.contains(format.toLowerCase())) {
            return ImageUploadResult.builder()
                    .success(false)
                    .errorMessage("Unsupported image format: " + format)
                    .originalFileName(fileName)
                    .fileSize(imageData.length)
                    .format(format)
                    .build();
        }

        return ImageUploadResult.builder().success(true).build();
    }

    /**
     * 上传到Stone服务
     *
     * @param inputStream 图片输入流
     * @param fileName 文件名
     * @param format 图片格式
     * @param fileSize 文件大小
     * @param user 用户信息
     * @return 上传后的文件路径
     * @throws Exception 上传异常
     */
    private String uploadToStoneService(InputStream inputStream, String fileName, String format,
                                        long fileSize, User user) throws Exception {
        try {
            // 企业ID转换
            String tenantId = user.getTenantId();
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));

            // 构建Stone上传请求
            StoneFileUploadRequest stoneFileUploadRequest = new StoneFileUploadRequest();
            stoneFileUploadRequest.setEa(ea);
            stoneFileUploadRequest.setEmployeeId(Integer.valueOf(user.getUserId()));
            stoneFileUploadRequest.setFileSize(Math.toIntExact(fileSize));
            stoneFileUploadRequest.setOriginName(fileName);
            stoneFileUploadRequest.setExtensionName(format);
            stoneFileUploadRequest.setBusiness("dataloader");
            stoneFileUploadRequest.setExpireDay(7);

            // 默认存储类型
            String storageType = "n";

            // 调用Stone API上传
            StoneFileUploadResponse response = stoneProxyApi.tempFileUploadByStream(storageType, stoneFileUploadRequest, inputStream);
            // 返回上传路径
            if (response != null && response.getPath() != null) {
                return response.getPath();
            } else {
                throw new RuntimeException("Upload response is null or path is empty");
            }

        } catch (Exception e) {
            log.error("Stone API upload failed: {}", fileName, e);
            throw new RuntimeException("Upload failed: " + fileName, e);
        }
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化的大小字符串
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
}
